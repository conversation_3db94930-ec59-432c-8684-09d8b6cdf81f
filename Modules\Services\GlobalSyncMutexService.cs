using System;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using Npgsql;
using ProManage.Modules.Connections;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Service for managing global synchronization locks
    /// Provides both process-level and cross-machine locking
    /// </summary>
    public static class GlobalSyncMutexService
    {
        private static readonly Mutex _syncMutex = new Mutex(false, "ProManage_FormSync_Mutex");
        private static bool _mutexAcquired = false;
        private static NpgsqlConnection _advisoryLockConnection = null;
        private static readonly long _advisoryLockKey = GenerateAdvisoryLockKey();

        /// <summary>
        /// Try to acquire sync lock with timeout
        /// </summary>
        /// <param name="timeout">Maximum time to wait for lock</param>
        /// <returns>True if lock acquired successfully</returns>
        public static bool TryAcquireSyncLock(TimeSpan timeout)
        {
            try
            {
                // First acquire process-level mutex
                if (_syncMutex.WaitOne(timeout))
                {
                    _mutexAcquired = true;

                    // Then try to acquire database advisory lock
                    var connectionString = DatabaseConnectionManager.Instance.Connection?.ConnectionString;
                    if (TryAcquireDbAdvisoryLock(connectionString))
                    {
                        return true;
                    }
                    else
                    {
                        // Failed to get DB lock, release mutex
                        ReleaseSyncLock();
                        return false;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error acquiring sync lock: {ex.Message}");
                ReleaseSyncLock(); // Cleanup on error
                return false;
            }
        }

        /// <summary>
        /// Release sync lock
        /// </summary>
        public static void ReleaseSyncLock()
        {
            try
            {
                // Release database advisory lock first
                if (_advisoryLockConnection != null)
                {
                    ReleaseDbAdvisoryLock();
                }

                // Then release process mutex
                if (_mutexAcquired)
                {
                    _syncMutex.ReleaseMutex();
                    _mutexAcquired = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error releasing sync lock: {ex.Message}");
            }
        }

        /// <summary>
        /// Check if sync is currently in progress
        /// </summary>
        /// <returns>True if sync is in progress</returns>
        public static bool IsSyncInProgress()
        {
            return _mutexAcquired && _advisoryLockConnection != null;
        }

        /// <summary>
        /// Try to acquire PostgreSQL advisory lock for cross-machine safety
        /// </summary>
        /// <param name="connectionString">Database connection string</param>
        /// <returns>True if advisory lock acquired</returns>
        public static bool TryAcquireDbAdvisoryLock(string connectionString)
        {
            try
            {
                if (_advisoryLockConnection != null)
                {
                    return true; // Already have lock
                }

                _advisoryLockConnection = new NpgsqlConnection(connectionString);
                _advisoryLockConnection.Open();

                using (var command = new NpgsqlCommand("SELECT pg_try_advisory_lock(@lockKey)", _advisoryLockConnection))
                {
                    command.Parameters.AddWithValue("@lockKey", _advisoryLockKey);
                    var result = command.ExecuteScalar();
                    
                    if (result is bool lockAcquired && lockAcquired)
                    {
                        return true;
                    }
                    else
                    {
                        // Failed to acquire lock, cleanup connection
                        _advisoryLockConnection.Close();
                        _advisoryLockConnection.Dispose();
                        _advisoryLockConnection = null;
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error acquiring DB advisory lock: {ex.Message}");
                
                // Cleanup on error
                if (_advisoryLockConnection != null)
                {
                    try
                    {
                        _advisoryLockConnection.Close();
                        _advisoryLockConnection.Dispose();
                    }
                    catch { }
                    _advisoryLockConnection = null;
                }
                
                return false;
            }
        }

        /// <summary>
        /// Release PostgreSQL advisory lock
        /// </summary>
        public static void ReleaseDbAdvisoryLock()
        {
            try
            {
                if (_advisoryLockConnection != null)
                {
                    using (var command = new NpgsqlCommand("SELECT pg_advisory_unlock(@lockKey)", _advisoryLockConnection))
                    {
                        command.Parameters.AddWithValue("@lockKey", _advisoryLockKey);
                        command.ExecuteScalar();
                    }

                    _advisoryLockConnection.Close();
                    _advisoryLockConnection.Dispose();
                    _advisoryLockConnection = null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error releasing DB advisory lock: {ex.Message}");
                
                // Force cleanup even on error
                if (_advisoryLockConnection != null)
                {
                    try
                    {
                        _advisoryLockConnection.Close();
                        _advisoryLockConnection.Dispose();
                    }
                    catch { }
                    _advisoryLockConnection = null;
                }
            }
        }

        /// <summary>
        /// Generate consistent advisory lock key using hash
        /// </summary>
        /// <returns>64-bit lock key</returns>
        public static long GenerateAdvisoryLockKey()
        {
            try
            {
                // Use consistent hash to avoid collisions with other tools
                using (var sha256 = SHA256.Create())
                {
                    var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes("ProManage_FormSync"));
                    return BitConverter.ToInt64(hashBytes, 0);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating advisory lock key: {ex.Message}");
                // Fallback to a fixed key if hash generation fails
                return 1234567890123456789L;
            }
        }

        /// <summary>
        /// Cleanup resources on application shutdown
        /// </summary>
        public static void Cleanup()
        {
            try
            {
                ReleaseSyncLock();
                _syncMutex?.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during cleanup: {ex.Message}");
            }
        }
    }
}
