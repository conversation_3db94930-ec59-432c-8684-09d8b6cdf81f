using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Tests.Discovery
{
    /// <summary>
    /// Unit tests for Database Synchronization functionality (Task 3 Implementation)
    /// Tests the SQL procedures and batch sync operations
    /// </summary>
    [TestClass]
    public class DatabaseSyncTests
    {
        [TestMethod]
        public void GetFormsFromDatabaseUsingProcedure_ValidDatabase_ReturnsFormList()
        {
            // Arrange & Act
            var result = PermissionSyncService.GetFormsFromDatabaseUsingProcedure();

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.IsInstanceOfType(result, typeof(List<string>), "Result should be a list of strings");
            // Note: We can't assert specific count as it depends on database state
        }

        [TestMethod]
        public void ValidateAndOptimizeDatabase_ValidDatabase_ReturnsTrue()
        {
            // Arrange & Act
            var result = PermissionSyncService.ValidateAndOptimizeDatabase();

            // Assert
            Assert.IsTrue(result, "Database validation and optimization should succeed");
        }

        [TestMethod]
        public async Task ExecuteBatchSyncAsync_EmptyLists_ReturnsSuccessWithNoChanges()
        {
            // Arrange
            var formsToAdd = new List<string>();
            var formsToRemove = new List<string>();

            // Act
            var result = await PermissionSyncService.ExecuteBatchSyncAsync(formsToAdd, formsToRemove);

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.IsTrue(result.SyncSuccess, "Sync should succeed with empty lists");
            Assert.IsTrue(result.Errors.Any(e => e.Contains("No forms to sync")), "Should indicate no forms to sync");
        }

        [TestMethod]
        public async Task ExecuteBatchSyncAsync_NullLists_ReturnsSuccessWithNoChanges()
        {
            // Arrange
            List<string> formsToAdd = null;
            List<string> formsToRemove = null;

            // Act
            var result = await PermissionSyncService.ExecuteBatchSyncAsync(formsToAdd, formsToRemove);

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.IsTrue(result.SyncSuccess, "Sync should succeed with null lists");
            Assert.IsTrue(result.Errors.Any(e => e.Contains("No forms to sync")), "Should indicate no forms to sync");
        }

        [TestMethod]
        public async Task ExecuteBatchSyncAsync_ValidForms_ReturnsDetailedResult()
        {
            // Arrange
            var formsToAdd = new List<string> { "TESTFORM1", "TESTFORM2" };
            var formsToRemove = new List<string> { "OLDFORM1" };
            var progressReports = new List<SyncProgress>();
            var progress = new Progress<SyncProgress>(p => progressReports.Add(p));

            // Act
            var result = await PermissionSyncService.ExecuteBatchSyncAsync(formsToAdd, formsToRemove, progress);

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.IsNotNull(result.MissingForms, "MissingForms should not be null");
            Assert.IsNotNull(result.ObsoleteForms, "ObsoleteForms should not be null");
            Assert.IsTrue(result.SyncDuration > TimeSpan.Zero, "Sync duration should be recorded");
            Assert.IsTrue(progressReports.Count > 0, "Progress should be reported");
        }

        [TestMethod]
        public void GetConfigurableTimeout_DefaultSettings_ReturnsDefaultTimeout()
        {
            // Arrange & Act
            var timeout = PermissionSyncService.GetConfigurableTimeout();

            // Assert
            Assert.AreEqual(TimeSpan.FromMinutes(5), timeout, "Default timeout should be 5 minutes");
        }

        [TestMethod]
        public void ValidatePermissionTables_ValidDatabase_ReturnsTrue()
        {
            // Arrange & Act
            var result = PermissionSyncService.ValidatePermissionTables();

            // Assert
            Assert.IsTrue(result, "Permission tables validation should succeed");
        }

        [TestMethod]
        public void ReportProgress_ValidProgress_DoesNotThrow()
        {
            // Arrange
            var progressReports = new List<SyncProgress>();
            var progress = new Progress<SyncProgress>(p => progressReports.Add(p));

            // Act & Assert
            try
            {
                PermissionSyncService.ReportProgress(progress, 50, 100, "Test operation");
                // If we reach here, no exception was thrown
            }
            catch (Exception ex)
            {
                Assert.Fail($"Expected no exception, but got: {ex.Message}");
            }
            
            Assert.AreEqual(1, progressReports.Count, "Progress should be reported once");
            Assert.AreEqual(50, progressReports[0].CompletedOperations, "Completed operations should match");
            Assert.AreEqual(100, progressReports[0].TotalOperations, "Total operations should match");
            Assert.AreEqual("Test operation", progressReports[0].CurrentOperation, "Operation description should match");
        }

        [TestMethod]
        public void ReportProgress_NullProgress_DoesNotThrow()
        {
            // Arrange
            IProgress<SyncProgress> progress = null;

            // Act & Assert
            try
            {
                PermissionSyncService.ReportProgress(progress, 50, 100, "Test operation");
                // If we reach here, no exception was thrown
            }
            catch (Exception ex)
            {
                Assert.Fail($"Expected no exception, but got: {ex.Message}");
            }
        }

        [TestMethod]
        public async Task ExecuteBatchSyncAsync_ConcurrentCalls_HandlesAdvisoryLock()
        {
            // Arrange
            var formsToAdd = new List<string> { "CONCURRENTTEST1" };
            var formsToRemove = new List<string>();

            // Act - Start two concurrent sync operations
            var task1 = PermissionSyncService.ExecuteBatchSyncAsync(formsToAdd, formsToRemove);
            var task2 = PermissionSyncService.ExecuteBatchSyncAsync(formsToAdd, formsToRemove);

            var results = await Task.WhenAll(task1, task2);

            // Assert
            Assert.IsNotNull(results[0], "First result should not be null");
            Assert.IsNotNull(results[1], "Second result should not be null");
            
            // At least one should succeed, one might fail due to advisory lock
            var successCount = results.Count(r => r.SyncSuccess);
            var lockFailureCount = results.Count(r => 
                !r.SyncSuccess && r.Errors.Any(e => e.Contains("already in progress")));
            
            Assert.IsTrue(successCount >= 1, "At least one sync should succeed");
            // Note: Advisory lock behavior depends on timing and database state
        }

        [TestMethod]
        public void FormSyncResult_DefaultValues_AreCorrect()
        {
            // Arrange & Act
            var result = new FormSyncResult();

            // Assert
            Assert.IsFalse(result.HasMismatch, "HasMismatch should default to false");
            Assert.IsFalse(result.SyncSuccess, "SyncSuccess should default to false");
            Assert.IsNotNull(result.MissingForms, "MissingForms should be initialized");
            Assert.IsNotNull(result.ObsoleteForms, "ObsoleteForms should be initialized");
            Assert.IsNotNull(result.ExistingForms, "ExistingForms should be initialized");
            Assert.IsNotNull(result.Errors, "Errors should be initialized");
            Assert.AreEqual(0, result.TotalFormsProcessed, "TotalFormsProcessed should default to 0");
            Assert.AreEqual(0, result.UsersAffected, "UsersAffected should default to 0");
            Assert.AreEqual(0, result.RolesAffected, "RolesAffected should default to 0");
        }

        [TestMethod]
        public void SyncProgress_PercentComplete_CalculatesCorrectly()
        {
            // Arrange
            var progress = new SyncProgress
            {
                TotalOperations = 100,
                CompletedOperations = 25
            };

            // Act
            var percent = progress.PercentComplete;

            // Assert
            Assert.AreEqual(25, percent, "Percent complete should be calculated correctly");
        }

        [TestMethod]
        public void SyncProgress_EstimatedTimeRemaining_CalculatesCorrectly()
        {
            // Arrange
            var progress = new SyncProgress
            {
                TotalOperations = 100,
                CompletedOperations = 25,
                StartTime = DateTime.Now.AddMinutes(-1) // Started 1 minute ago
            };

            // Act
            var timeRemaining = progress.EstimatedTimeRemaining;

            // Assert
            Assert.IsTrue(timeRemaining > TimeSpan.Zero, "Estimated time remaining should be positive");
            Assert.IsTrue(timeRemaining < TimeSpan.FromMinutes(10), "Estimated time should be reasonable");
        }
    }
}
