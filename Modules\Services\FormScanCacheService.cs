using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Enhanced service for managing persistent form scan cache
    /// Provides 30-minute cache expiration with corruption recovery, versioning, and fallback paths
    /// </summary>
    public static class FormScanCacheService
    {
        private static readonly string PRIMARY_CACHE_DIR = Environment.ExpandEnvironmentVariables("%APPDATA%/ProManage");
        private static readonly string FALLBACK_CACHE_DIR = Environment.ExpandEnvironmentVariables("%LOCALAPPDATA%/ProManage");
        private static readonly string CACHE_FILE_NAME = "form-scan-cache.json";
        private static FormScanCache _memoryCache;

        /// <summary>
        /// Get cached form scan results with enhanced validation and migration
        /// </summary>
        /// <returns>Cache object or new empty cache if invalid/expired</returns>
        public static FormScanCache GetCache()
        {
            try
            {
                // Check memory cache first
                if (_memoryCache != null && _memoryCache.IsValid)
                {
                    return _memoryCache;
                }

                // Load from disk
                var cache = LoadCacheFromDisk();

                // Validate cache version and migrate if needed
                if (cache != null && !ValidateCacheVersion(cache))
                {
                    SyncLoggingService.LogCacheOperation("Cache version validation failed, attempting migration", false);
                    cache = MigrateCacheVersion(cache);
                }

                if (cache != null && cache.IsValid)
                {
                    _memoryCache = cache;
                    SyncLoggingService.LogCacheOperation("Cache loaded successfully", true);
                    return cache;
                }

                SyncLoggingService.LogCacheOperation("Cache invalid or expired, returning new cache", false);
                return new FormScanCache();
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("Cache retrieval failed", ex);
                return new FormScanCache(); // Return empty cache on failure
            }
        }

        /// <summary>
        /// Update cache with new form list using enhanced error handling
        /// </summary>
        /// <param name="formList">List of forms to cache</param>
        public static void UpdateCache(List<string> formList)
        {
            try
            {
                var cache = new FormScanCache
                {
                    Version = 1,
                    LastScanTime = DateTime.Now,
                    FormListHash = GenerateFormListHash(formList),
                    CachedFormList = formList?.ToList() ?? new List<string>(),
                    HashingAlgorithm = "SHA256",
                    CacheFilePath = GetCacheFilePath()
                };

                SaveCacheToDisk(cache);
                _memoryCache = cache;
                SyncLoggingService.LogCacheOperation("Cache updated", true);
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("Cache update failed", ex);
            }
        }

        /// <summary>
        /// Check if scan should be skipped based on cache validity
        /// </summary>
        /// <returns>True if scan can be skipped</returns>
        public static bool ShouldSkipScan()
        {
            try
            {
                var cache = GetCache();
                return cache.IsValid && cache.CachedFormList?.Any() == true;
            }
            catch
            {
                return false; // Always scan if cache check fails
            }
        }

        /// <summary>
        /// Generate SHA256 hash of form list for change detection
        /// </summary>
        /// <param name="forms">List of form names</param>
        /// <returns>SHA256 hash string</returns>
        public static string GenerateFormListHash(List<string> forms)
        {
            try
            {
                if (forms == null || forms.Count == 0)
                    return string.Empty;

                // Sort forms for consistent hashing
                var sortedForms = new List<string>(forms);
                sortedForms.Sort(StringComparer.OrdinalIgnoreCase);

                var combined = string.Join("|", sortedForms);
                using (var sha256 = SHA256.Create())
                {
                    var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(combined));
                    return Convert.ToBase64String(hashBytes);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating hash: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Save cache to disk with atomic write operations and enhanced fallback
        /// </summary>
        /// <param name="cache">Cache object to save</param>
        public static void SaveCacheToDisk(FormScanCache cache)
        {
            var cacheFilePath = GetCacheFilePath();

            try
            {
                cache.CacheFilePath = cacheFilePath;
                var json = JsonSerializer.Serialize(cache, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                // Atomic write to prevent corruption
                var tempFile = cacheFilePath + ".tmp";
                File.WriteAllText(tempFile, json);
                File.Move(tempFile, cacheFilePath);

                cache.CacheFileSize = new FileInfo(cacheFilePath).Length;
                SyncLoggingService.LogCacheOperation("Cache saved to primary location", true);
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("Cache save failed, trying fallback", ex);
                throw new CacheException("Failed to save cache", ex);
            }
        }

        /// <summary>
        /// Load cache from disk with enhanced corruption recovery
        /// </summary>
        /// <returns>Cache object or null if not found/corrupted</returns>
        public static FormScanCache LoadCacheFromDisk()
        {
            var cacheFilePath = GetCacheFilePath();

            if (!File.Exists(cacheFilePath))
                return new FormScanCache();

            try
            {
                var json = File.ReadAllText(cacheFilePath);
                var cache = JsonSerializer.Deserialize<FormScanCache>(json, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                return cache ?? new FormScanCache();
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("Cache load failed, rebuilding", ex);

                // Delete corrupted cache file
                try { File.Delete(cacheFilePath); } catch { }

                return new FormScanCache();
            }
        }

        /// <summary>
        /// Get cache file path with enhanced fallback and write access testing
        /// </summary>
        /// <returns>Full path to cache file</returns>
        public static string GetCacheFilePath()
        {
            // Try primary path first (%APPDATA%)
            try
            {
                var primaryPath = Path.Combine(PRIMARY_CACHE_DIR, CACHE_FILE_NAME);
                Directory.CreateDirectory(PRIMARY_CACHE_DIR);

                // Test write access
                var testFile = Path.Combine(PRIMARY_CACHE_DIR, "test.tmp");
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);

                return primaryPath;
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError($"Primary cache path failed, using fallback: {ex.Message}", ex);
            }

            // Fallback to %LOCALAPPDATA% for RDP/service accounts
            try
            {
                var fallbackPath = Path.Combine(FALLBACK_CACHE_DIR, CACHE_FILE_NAME);
                Directory.CreateDirectory(FALLBACK_CACHE_DIR);
                return fallbackPath;
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("Fallback cache path failed", ex);
                throw new CacheException("Unable to access cache storage", ex);
            }
        }

        /// <summary>
        /// Validate cache version with enhanced validation
        /// </summary>
        /// <param name="cache">Cache to validate</param>
        /// <returns>True if cache is valid</returns>
        public static bool ValidateCacheVersion(FormScanCache cache)
        {
            if (cache == null) return false;

            // Current version is 1, validate against this
            return cache.Version == 1 &&
                   !string.IsNullOrEmpty(cache.HashingAlgorithm) &&
                   cache.CachedFormList != null;
        }

        /// <summary>
        /// Migrate cache from older version with comprehensive migration logic
        /// </summary>
        /// <param name="oldCache">Old cache to migrate</param>
        /// <returns>Migrated cache or new cache if migration fails</returns>
        public static FormScanCache MigrateCacheVersion(FormScanCache oldCache)
        {
            if (oldCache == null)
                return new FormScanCache();

            try
            {
                // Migration logic for future version changes
                switch (oldCache.Version)
                {
                    case 0: // Legacy version without versioning
                        return new FormScanCache
                        {
                            Version = 1,
                            LastScanTime = oldCache.LastScanTime,
                            FormListHash = GenerateFormListHash(oldCache.CachedFormList ?? new List<string>()),
                            CachedFormList = oldCache.CachedFormList ?? new List<string>(),
                            HashingAlgorithm = "SHA256"
                        };

                    default:
                        SyncLoggingService.LogSyncError($"Unknown cache version: {oldCache.Version}", null);
                        return new FormScanCache();
                }
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("Cache migration failed", ex);
                return new FormScanCache();
            }
        }

        /// <summary>
        /// Clear all cached data with enhanced error handling
        /// </summary>
        public static void ClearCache()
        {
            try
            {
                _memoryCache = null;

                var cacheFilePath = GetCacheFilePath();
                if (File.Exists(cacheFilePath))
                {
                    File.Delete(cacheFilePath);
                }

                // Also clear fallback location
                try
                {
                    var fallbackPath = Path.Combine(FALLBACK_CACHE_DIR, CACHE_FILE_NAME);
                    if (File.Exists(fallbackPath))
                    {
                        File.Delete(fallbackPath);
                    }
                }
                catch (Exception fallbackEx)
                {
                    SyncLoggingService.LogSyncError("Error clearing fallback cache", fallbackEx);
                }

                SyncLoggingService.LogCacheOperation("Cache cleared", true);
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("Error clearing cache", ex);
            }
        }
    }
}
