using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Tests.Discovery
{
    /// <summary>
    /// Comprehensive unit tests for Task 6: Testing Strategy and Scenarios
    /// Advanced testing for all Form Discovery Service components
    /// </summary>
    [TestClass]
    public class ComprehensiveUnitTests
    {
        [TestInitialize]
        public void Setup()
        {
            // Initialize logging for tests
            SyncLoggingService.ConfigureRollingFileLogger();
        }

        #region PermissionSyncService Tests

        [TestMethod]
        public void PermissionSyncService_ValidateFormName_RejectsInvalidCharacters()
        {
            // Test SQL injection prevention
            var invalidNames = new[]
            {
                "Form'; DROP TABLE users; --",
                "Form\"",
                "Form;",
                "Form--",
                "Form/",
                "Form\\",
                "Form<script>",
                "Form|",
                ""
            };

            foreach (var invalidName in invalidNames)
            {
                var result = PermissionSyncService.ValidateFormName(invalidName);
                Assert.IsFalse(result, $"Form name '{invalidName}' should be rejected");
            }
        }

        [TestMethod]
        public void PermissionSyncService_ValidateFormName_AcceptsValidNames()
        {
            var validNames = new[]
            {
                "MainForm",
                "DatabaseForm",
                "Permission_Management_Form",
                "Form123",
                "TEST_FORM"
            };

            foreach (var validName in validNames)
            {
                var result = PermissionSyncService.ValidateFormName(validName);
                Assert.IsTrue(result, $"Form name '{validName}' should be accepted");
            }
        }

        [TestMethod]
        public void PermissionSyncService_ValidateFormName_RejectsLongNames()
        {
            var longName = new string('A', 101); // 101 characters
            var result = PermissionSyncService.ValidateFormName(longName);
            Assert.IsFalse(result, "Form names over 100 characters should be rejected");
        }

        #endregion

        #region FormScanCacheService Tests

        [TestMethod]
        public void FormScanCacheService_CacheVersionMigration_HandlesLegacyCache()
        {
            // Arrange
            var legacyCache = new FormScanCache
            {
                Version = 0, // Legacy version
                LastScanTime = DateTime.Now.AddMinutes(-10),
                CachedFormList = new List<string> { "LegacyForm1", "LegacyForm2" }
            };

            // Act
            var migratedCache = FormScanCacheService.MigrateCacheVersion(legacyCache);

            // Assert
            Assert.AreEqual(1, migratedCache.Version, "Cache should be migrated to version 1");
            Assert.AreEqual("SHA256", migratedCache.HashingAlgorithm, "Migrated cache should use SHA256");
            Assert.AreEqual(2, migratedCache.CachedFormList.Count, "Form list should be preserved");
            Assert.IsFalse(string.IsNullOrEmpty(migratedCache.FormListHash), "Hash should be generated");
        }

        [TestMethod]
        public void FormScanCacheService_CacheVersionMigration_HandlesUnknownVersion()
        {
            // Arrange
            var unknownCache = new FormScanCache
            {
                Version = 999, // Unknown future version
                LastScanTime = DateTime.Now,
                CachedFormList = new List<string> { "TestForm" }
            };

            // Act
            var result = FormScanCacheService.MigrateCacheVersion(unknownCache);

            // Assert
            Assert.AreEqual(1, result.Version, "Unknown version should result in new cache");
            Assert.IsNotNull(result.CachedFormList, "New cache should have empty form list");
        }

        [TestMethod]
        public void FormScanCacheService_ShouldSkipScan_HandlesEmptyCache()
        {
            // Arrange - Clear cache first
            FormScanCacheService.ClearCache();

            // Act
            var shouldSkip = FormScanCacheService.ShouldSkipScan();

            // Assert
            Assert.IsFalse(shouldSkip, "Should not skip scan with empty cache");
        }

        #endregion

        #region HealthCheckService Tests

        [TestMethod]
        public async Task HealthCheckService_GetSyncHealthStatus_RejectsInvalidApiKey()
        {
            // Act
            var result = await HealthCheckService.GetSyncHealthStatus("invalid-key", "localhost");

            // Assert
            Assert.AreEqual("Unauthorized", result.Status, "Should reject invalid API key");
            Assert.IsNotNull(result.Message, "Should provide error message");
            Assert.IsTrue(result.Timestamp > DateTime.MinValue, "Should set timestamp");
        }

        [TestMethod]
        public async Task HealthCheckService_GetSyncHealthStatus_RejectsNonLocalhostAccess()
        {
            // Act
            var result = await HealthCheckService.GetSyncHealthStatus("default-dev-key", "remote-host");

            // Assert
            Assert.AreEqual("Forbidden", result.Status, "Should reject non-localhost access");
            Assert.IsNotNull(result.Message, "Should provide error message");
        }

        [TestMethod]
        public async Task HealthCheckService_GetSyncHealthStatus_AcceptsValidRequest()
        {
            // Act
            var result = await HealthCheckService.GetSyncHealthStatus("default-dev-key", "localhost");

            // Assert
            Assert.IsNotNull(result.Status, "Should return status");
            Assert.IsTrue(result.Timestamp > DateTime.MinValue, "Should set timestamp");
            Assert.IsNotNull(result.Version, "Should include version");
        }

        #endregion

        #region ConfigurationService Tests

        [TestMethod]
        public void ConfigurationService_GetSyncConfiguration_ReturnsDefaults()
        {
            // Act
            var config = ConfigurationService.GetSyncConfiguration();

            // Assert
            Assert.IsNotNull(config, "Should return configuration");
            Assert.AreEqual(TimeSpan.FromMinutes(5), config.TransactionTimeout, "Should have correct default timeout");
            Assert.AreEqual(TimeSpan.FromMinutes(30), config.CacheExpiration, "Should have correct cache expiration");
            Assert.IsTrue(config.EnablePersistentCache, "Should enable persistent cache by default");
        }

        [TestMethod]
        public void ConfigurationService_GetDatabaseConfiguration_ReturnsDefaults()
        {
            // Act
            var config = ConfigurationService.GetDatabaseConfiguration();

            // Assert
            Assert.IsNotNull(config, "Should return database configuration");
            Assert.IsTrue(config.UseAdvisoryLocks, "Should use advisory locks by default");
            Assert.AreEqual("Serializable", config.IsolationLevel, "Should use Serializable isolation");
            Assert.AreEqual(TimeSpan.FromMinutes(2), config.CommandTimeout, "Should have correct command timeout");
        }

        [TestMethod]
        public void ConfigurationService_ReloadConfiguration_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => ConfigurationService.ReloadConfiguration(), 
                "Configuration reload should not throw exceptions");
        }

        #endregion

        #region PerformanceMonitoringService Tests

        [TestMethod]
        public void PerformanceMonitoringService_MeasureSyncPerformance_CapturesMetrics()
        {
            // Arrange
            var testOperation = new Func<FormSyncResult>(() =>
            {
                System.Threading.Thread.Sleep(100); // Simulate work
                return new FormSyncResult
                {
                    MissingForms = new List<string> { "TestForm1", "TestForm2" },
                    ObsoleteForms = new List<string> { "OldForm1" }
                };
            });

            // Act
            var metrics = PerformanceMonitoringService.MeasureSyncPerformance(testOperation);

            // Assert
            Assert.IsTrue(metrics.TotalSyncTime.TotalMilliseconds >= 90, "Should capture execution time");
            Assert.AreEqual(2, metrics.FormsAdded, "Should capture forms added count");
            Assert.AreEqual(1, metrics.FormsRemoved, "Should capture forms removed count");
        }

        [TestMethod]
        public void PerformanceMonitoringService_MeasureCacheOperation_CapturesDuration()
        {
            // Arrange
            var testOperation = new Action(() =>
            {
                System.Threading.Thread.Sleep(50); // Simulate cache work
            });

            // Act
            var duration = PerformanceMonitoringService.MeasureCacheOperation(testOperation, "TestCacheOp");

            // Assert
            Assert.IsTrue(duration.TotalMilliseconds >= 40, "Should capture cache operation duration");
        }

        [TestMethod]
        public void PerformanceMonitoringService_GetMemoryUsage_ReturnsValidData()
        {
            // Act
            var memoryInfo = PerformanceMonitoringService.GetMemoryUsage();

            // Assert
            Assert.IsTrue(memoryInfo.TotalMemory > 0, "Should return positive memory usage");
            Assert.IsTrue(memoryInfo.Gen0Collections >= 0, "Should return valid GC collection count");
            Assert.IsTrue(memoryInfo.Timestamp > DateTime.MinValue, "Should set timestamp");
        }

        #endregion

        #region GlobalSyncMutexService Tests

        [TestMethod]
        public void GlobalSyncMutexService_GenerateAdvisoryLockKey_IsConsistent()
        {
            // Act
            var key1 = GlobalSyncMutexService.GenerateAdvisoryLockKey();
            var key2 = GlobalSyncMutexService.GenerateAdvisoryLockKey();

            // Assert
            Assert.AreEqual(key1, key2, "Advisory lock key should be consistent across calls");
            Assert.IsTrue(key1 > 0, "Advisory lock key should be positive");
        }

        [TestMethod]
        public void GlobalSyncMutexService_IsSyncInProgress_InitiallyFalse()
        {
            // Act
            var inProgress = GlobalSyncMutexService.IsSyncInProgress();

            // Assert
            Assert.IsFalse(inProgress, "Sync should not be in progress initially");
        }

        #endregion

        #region Error Handling Tests

        [TestMethod]
        public void FormScanCacheService_HandlesNullFormList()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => FormScanCacheService.UpdateCache(null), 
                "Should handle null form list gracefully");
        }

        [TestMethod]
        public void FormScanCacheService_HandlesEmptyFormList()
        {
            // Arrange
            var emptyList = new List<string>();

            // Act & Assert
            Assert.DoesNotThrow(() => FormScanCacheService.UpdateCache(emptyList), 
                "Should handle empty form list gracefully");
        }

        [TestMethod]
        public void PermissionSyncService_HandlesNullFormName()
        {
            // Act
            var result = PermissionSyncService.ValidateFormName(null);

            // Assert
            Assert.IsFalse(result, "Should reject null form name");
        }

        #endregion
    }
}
