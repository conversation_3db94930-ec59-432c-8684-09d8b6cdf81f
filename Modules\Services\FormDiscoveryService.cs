using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using ProManage.Modules.Connections;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Enhanced Form Discovery Service with caching and filtering
    /// Scans MainForms folder and compares with database permission tables
    /// </summary>
    public static class FormDiscoveryService
    {
        private static readonly string MainFormsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Forms", "MainForms");

        /// <summary>
        /// Get list of forms from file system with filtering and normalization
        /// </summary>
        /// <returns>List of normalized form names</returns>
        public static List<string> GetFormsFromFileSystem()
        {
            var startTime = DateTime.Now;
            
            try
            {
                if (!Directory.Exists(MainFormsPath))
                {
                    SyncLoggingService.LogSyncError("GetFormsFromFileSystem", 
                        new DirectoryNotFoundException($"MainForms directory not found: {MainFormsPath}"));
                    return new List<string>();
                }

                var formFiles = Directory.GetFiles(MainFormsPath, "*.cs")
                    .Where(f => IsValidFormFile(f))
                    .Select(f => Path.GetFileNameWithoutExtension(f))
                    .Where(f => !string.IsNullOrEmpty(f))
                    .ToList();

                // Filter out test files and normalize names
                var validForms = FilterValidFormNames(formFiles);
                var normalizedForms = validForms.Select(NormalizeFormName).ToList();

                var duration = DateTime.Now - startTime;
                SyncLoggingService.LogFormDiscovery("GetFormsFromFileSystem", normalizedForms.Count, duration);

                return normalizedForms.OrderBy(f => f).ToList();
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("GetFormsFromFileSystem", ex);
                return new List<string>();
            }
        }

        /// <summary>
        /// Get list of forms from database permission tables
        /// </summary>
        /// <returns>List of normalized form names from database</returns>
        public static List<string> GetFormsFromDatabase()
        {
            var startTime = DateTime.Now;
            
            try
            {
                var formNames = PermissionDatabaseService.GetAllFormsFromDatabase();
                var normalizedForms = formNames.Select(NormalizeFormName).Distinct().ToList();

                var duration = DateTime.Now - startTime;
                SyncLoggingService.LogFormDiscovery("GetFormsFromDatabase", normalizedForms.Count, duration);

                return normalizedForms.OrderBy(f => f).ToList();
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("GetFormsFromDatabase", ex);
                return new List<string>();
            }
        }

        /// <summary>
        /// Compare forms between file system and database
        /// </summary>
        /// <returns>Comparison result with mismatch details</returns>
        public static FormSyncResult CompareFormsWithDatabase()
        {
            var result = new FormSyncResult
            {
                SyncTimestamp = DateTime.Now
            };

            try
            {
                // Check cache first
                if (FormScanCacheService.ShouldSkipScan())
                {
                    var cache = FormScanCacheService.GetCache();
                    if (cache != null)
                    {
                        result.ExistingForms = cache.CachedFormList;
                        result.HasMismatch = false;
                        SyncLoggingService.LogCacheOperation("CompareFormsWithDatabase", true, "Used cached results");
                        return result;
                    }
                }

                var fileSystemForms = GetFormsFromFileSystem();
                var databaseForms = GetFormsFromDatabase();

                // Find missing forms (in filesystem but not in database)
                result.MissingForms = fileSystemForms
                    .Except(databaseForms, StringComparer.OrdinalIgnoreCase)
                    .ToList();

                // Find obsolete forms (in database but not in filesystem)
                result.ObsoleteForms = databaseForms
                    .Except(fileSystemForms, StringComparer.OrdinalIgnoreCase)
                    .ToList();

                // Find existing forms (in both)
                result.ExistingForms = fileSystemForms
                    .Intersect(databaseForms, StringComparer.OrdinalIgnoreCase)
                    .ToList();

                result.HasMismatch = result.MissingForms.Count > 0 || result.ObsoleteForms.Count > 0;
                result.TotalFormsProcessed = fileSystemForms.Count;

                // Update cache with current file system forms
                FormScanCacheService.UpdateCache(fileSystemForms);

                return result;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Error comparing forms: {ex.Message}");
                SyncLoggingService.LogSyncError("CompareFormsWithDatabase", ex);
                return result;
            }
        }

        /// <summary>
        /// Check if a file is a valid form file
        /// </summary>
        /// <param name="filePath">Full path to file</param>
        /// <returns>True if file should be included</returns>
        public static bool IsValidFormFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            var fileName = Path.GetFileName(filePath);
            
            // Must be .cs file
            if (!fileName.EndsWith(".cs", StringComparison.OrdinalIgnoreCase))
                return false;

            // Exclude designer files
            if (fileName.EndsWith(".Designer.cs", StringComparison.OrdinalIgnoreCase))
                return false;

            // Exclude resource files (shouldn't be .cs but just in case)
            if (fileName.EndsWith(".resx", StringComparison.OrdinalIgnoreCase))
                return false;

            return true;
        }

        /// <summary>
        /// Filter out test files and other invalid form names
        /// CRITICAL: Excludes *Tests.cs files to avoid test harness permissions
        /// </summary>
        /// <param name="formNames">List of form names to filter</param>
        /// <returns>Filtered list of valid form names</returns>
        public static List<string> FilterValidFormNames(List<string> formNames)
        {
            if (formNames == null)
                return new List<string>();

            return formNames
                .Where(name => !string.IsNullOrEmpty(name))
                .Where(name => !name.EndsWith("Tests", StringComparison.OrdinalIgnoreCase))
                .Where(name => !name.EndsWith("Test", StringComparison.OrdinalIgnoreCase))
                .ToList();
        }

        /// <summary>
        /// Normalize form name using UpperInvariant standard
        /// CRITICAL: Ensures consistent form name comparison
        /// </summary>
        /// <param name="formName">Form name to normalize</param>
        /// <returns>Normalized form name</returns>
        public static string NormalizeFormName(string formName)
        {
            if (string.IsNullOrEmpty(formName))
                return string.Empty;

            return formName.ToUpperInvariant();
        }

        /// <summary>
        /// Get detailed mismatch information
        /// </summary>
        /// <returns>Detailed mismatch analysis</returns>
        public static FormMismatchDetails GetMismatchDetails()
        {
            try
            {
                var fileSystemForms = GetFormsFromFileSystem();
                var databaseForms = GetFormsFromDatabase();

                return new FormMismatchDetails
                {
                    FormsOnlyInFileSystem = fileSystemForms
                        .Except(databaseForms, StringComparer.OrdinalIgnoreCase)
                        .ToList(),
                    FormsOnlyInDatabase = databaseForms
                        .Except(fileSystemForms, StringComparer.OrdinalIgnoreCase)
                        .ToList(),
                    FormsInBoth = fileSystemForms
                        .Intersect(databaseForms, StringComparer.OrdinalIgnoreCase)
                        .ToList()
                };
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("GetMismatchDetails", ex);
                return new FormMismatchDetails();
            }
        }

        /// <summary>
        /// Get sync status summary
        /// </summary>
        /// <returns>Human-readable sync status</returns>
        public static string GetSyncStatus()
        {
            try
            {
                var comparison = CompareFormsWithDatabase();
                
                if (comparison.Errors.Count > 0)
                {
                    return $"Error: {string.Join(", ", comparison.Errors)}";
                }

                if (!comparison.HasMismatch)
                {
                    return $"In sync: {comparison.ExistingForms.Count} forms";
                }

                return $"Out of sync: {comparison.MissingForms.Count} missing, {comparison.ObsoleteForms.Count} obsolete";
            }
            catch (Exception ex)
            {
                return $"Error checking sync status: {ex.Message}";
            }
        }

        /// <summary>
        /// Get form information with metadata
        /// </summary>
        /// <param name="formName">Form name</param>
        /// <returns>Form information or null if not found</returns>
        public static FormInfo GetFormInfo(string formName)
        {
            try
            {
                var formPath = Path.Combine(MainFormsPath, $"{formName}.cs");
                
                if (!File.Exists(formPath))
                    return null;

                var fileInfo = new FileInfo(formPath);
                
                return new FormInfo
                {
                    FormName = formName,
                    FilePath = formPath,
                    LastModified = fileInfo.LastWriteTime,
                    IsValid = IsValidFormFile(formPath),
                    FileSize = fileInfo.Length,
                    NormalizedName = NormalizeFormName(formName)
                };
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting form info for {formName}: {ex.Message}");
                return null;
            }
        }
    }
}
