using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Tests.Discovery
{
    /// <summary>
    /// Unit tests for Form Discovery Service implementation
    /// Tests the first 2 tasks: Core Services and Data Models
    /// </summary>
    [TestClass]
    public class FormDiscoveryServiceTests
    {
        [TestMethod]
        public void FilterValidFormNames_TestFiles_ExcludesTestFiles()
        {
            // Arrange
            var formNames = new List<string> 
            { 
                "MainForm", 
                "TestForm", 
                "SampleTests", 
                "UnitTests",
                "DatabaseForm",
                "PermissionTests",
                "EstimateFormTests"
            };

            // Act
            var result = FormDiscoveryService.FilterValidFormNames(formNames);

            // Assert
            Assert.IsFalse(result.Any(f => f.Contains("Tests")), "Test files should be excluded");
            Assert.IsTrue(result.Contains("MainForm"), "Valid forms should be included");
            Assert.IsTrue(result.Contains("TestForm"), "Forms with 'Test' in name but not ending with 'Tests' should be included");
            Assert.IsTrue(result.Contains("DatabaseForm"), "Valid forms should be included");
        }

        [TestMethod]
        public void NormalizeFormName_MixedCase_ReturnsUpperInvariant()
        {
            // Arrange
            var testCases = new Dictionary<string, string>
            {
                { "testForm", "TESTFORM" },
                { "DatabaseForm", "DATABASEFORM" },
                { "permissionManagementForm", "PERMISSIONMANAGEMENTFORM" },
                { "ALREADYUPPER", "ALREADYUPPER" },
                { "mixedCASE", "MIXEDCASE" }
            };

            // Act & Assert
            foreach (var testCase in testCases)
            {
                var result = FormDiscoveryService.NormalizeFormName(testCase.Key);
                Assert.AreEqual(testCase.Value, result, $"Form name '{testCase.Key}' should normalize to '{testCase.Value}'");
            }
        }

        [TestMethod]
        public void IsValidFormFile_DesignerFile_ReturnsFalse()
        {
            // Arrange
            var testFiles = new Dictionary<string, bool>
            {
                { "MainForm.cs", true },
                { "MainForm.Designer.cs", false },
                { "TestForm.resx", false },
                { "DatabaseForm.cs", true },
                { "SomeFile.txt", false },
                { "", false },
                { null, false }
            };

            // Act & Assert
            foreach (var testFile in testFiles)
            {
                var result = FormDiscoveryService.IsValidFormFile(testFile.Key);
                Assert.AreEqual(testFile.Value, result, $"File '{testFile.Key}' validation should return {testFile.Value}");
            }
        }

        [TestMethod]
        public void FormScanCache_IsValid_ChecksExpiration()
        {
            // Arrange
            var cache = new FormScanCache
            {
                LastScanTime = DateTime.Now.AddMinutes(-25), // Within 30-minute window
                FormListHash = "test-hash",
                CachedFormList = new List<string> { "TestForm" }
            };

            // Act
            var isValid = cache.IsValid;

            // Assert
            Assert.IsTrue(isValid, "Cache should be valid within 30-minute window");

            // Test expired cache
            cache.LastScanTime = DateTime.Now.AddMinutes(-35); // Outside 30-minute window
            isValid = cache.IsValid;
            Assert.IsFalse(isValid, "Cache should be invalid after 30 minutes");
        }

        [TestMethod]
        public void SyncProgress_PercentComplete_CalculatesCorrectly()
        {
            // Arrange
            var progress = new SyncProgress
            {
                TotalOperations = 100,
                CompletedOperations = 25
            };

            // Act
            var percent = progress.PercentComplete;

            // Assert
            Assert.AreEqual(25, percent, "Percent complete should be calculated correctly");

            // Test edge cases
            progress.TotalOperations = 0;
            Assert.AreEqual(0, progress.PercentComplete, "Should handle zero total operations");

            progress.TotalOperations = 10;
            progress.CompletedOperations = 10;
            Assert.AreEqual(100, progress.PercentComplete, "Should handle 100% completion");
        }

        [TestMethod]
        public void FormMismatchDetails_HasMismatches_CalculatesCorrectly()
        {
            // Arrange
            var mismatchDetails = new FormMismatchDetails
            {
                FormsOnlyInFileSystem = new List<string> { "NewForm1", "NewForm2" },
                FormsOnlyInDatabase = new List<string> { "OldForm1" },
                FormsInBoth = new List<string> { "ExistingForm1", "ExistingForm2" }
            };

            // Act & Assert
            Assert.AreEqual(3, mismatchDetails.TotalMismatches, "Should count mismatches correctly");
            Assert.IsTrue(mismatchDetails.HasMismatches, "Should detect mismatches");

            // Test no mismatches
            mismatchDetails.FormsOnlyInFileSystem.Clear();
            mismatchDetails.FormsOnlyInDatabase.Clear();
            Assert.AreEqual(0, mismatchDetails.TotalMismatches, "Should handle no mismatches");
            Assert.IsFalse(mismatchDetails.HasMismatches, "Should detect no mismatches");
        }

        [TestMethod]
        public void FormScanCacheService_GenerateFormListHash_ConsistentResults()
        {
            // Arrange
            var forms1 = new List<string> { "FormA", "FormB", "FormC" };
            var forms2 = new List<string> { "FormC", "FormA", "FormB" }; // Different order
            var forms3 = new List<string> { "FormA", "FormB", "FormD" }; // Different content

            // Act
            var hash1 = FormScanCacheService.GenerateFormListHash(forms1);
            var hash2 = FormScanCacheService.GenerateFormListHash(forms2);
            var hash3 = FormScanCacheService.GenerateFormListHash(forms3);

            // Assert
            Assert.AreEqual(hash1, hash2, "Hash should be same regardless of order");
            Assert.AreNotEqual(hash1, hash3, "Hash should be different for different content");
            Assert.IsFalse(string.IsNullOrEmpty(hash1), "Hash should not be empty");
        }

        [TestMethod]
        public void GlobalSyncMutexService_GenerateAdvisoryLockKey_ConsistentResults()
        {
            // Act
            var key1 = GlobalSyncMutexService.GenerateAdvisoryLockKey();
            var key2 = GlobalSyncMutexService.GenerateAdvisoryLockKey();

            // Assert
            Assert.AreEqual(key1, key2, "Advisory lock key should be consistent");
            Assert.AreNotEqual(0, key1, "Advisory lock key should not be zero");
        }

        [TestMethod]
        public void SyncLoggingService_IsConfigured_InitialState()
        {
            // Act
            var isConfigured = SyncLoggingService.IsConfigured();

            // Assert - Should be false initially since we haven't called ConfigureRollingFileLogger
            Assert.IsFalse(isConfigured, "Logging should not be configured initially");
        }

        [TestMethod]
        public void FormSyncException_Constructor_SetsPropertiesCorrectly()
        {
            // Arrange
            var formName = "TestForm";
            var operationType = SyncOperationType.AddForm;
            var message = "Test error message";

            // Act
            var exception = new FormSyncException(formName, operationType, message);

            // Assert
            Assert.AreEqual(formName, exception.FormName);
            Assert.AreEqual(operationType, exception.OperationType);
            Assert.AreEqual(message, exception.Message);
        }

        [TestMethod]
        public void SyncConfiguration_DefaultValues_AreCorrect()
        {
            // Arrange & Act
            var config = new SyncConfiguration();

            // Assert
            Assert.AreEqual(TimeSpan.FromMinutes(5), config.TransactionTimeout);
            Assert.AreEqual(TimeSpan.FromMinutes(30), config.CacheExpiration);
            Assert.IsTrue(config.EnablePersistentCache);
            Assert.IsTrue(config.EnableProgressReporting);
            Assert.IsTrue(config.EnableCrossMachineLocking);
            Assert.AreEqual(3, config.MaxRetryAttempts);
        }
    }
}
