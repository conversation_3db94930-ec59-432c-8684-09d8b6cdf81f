using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using ProManage.Forms.ChildForms;
using ProManage.Modules.Helpers.PermissionManagementForm;
using ProManage.Modules.Models.PermissionManagementForm;
using ProManage.Modules.Models;
using ProManage.Modules.Services;

namespace ProManage.Forms
{
    /// <summary>
    /// Comprehensive permission management form with 3 tabs for role permissions,
    /// user permissions, and global user management permissions.
    /// </summary>
    public partial class PermissionManagementForm : XtraForm
    {
        #region Private Fields

        private readonly PermissionGridHelper _gridHelper;
        private bool _isLoading = false;
        private int _currentRoleId = 0;
        private int _currentUserId = 0;
        private bool _hasUnsavedChanges = false;

        // Form Discovery and Sync Fields (Task 4 Implementation)
        private bool _isSyncInProgress = false;
        private readonly object _syncLock = new object();
        private IProgress<SyncProgress> _progressReporter;
        private CancellationTokenSource _cancellationTokenSource;

        // UI Components for mismatch detection and progress
        private Label _lblMismatchStatus;
        private Label _lblMismatchDetails;
        private System.Windows.Forms.ProgressBar _progressBarSync;
        private Label _lblCurrentOperation;
        private Label _lblProgress;
        private Label _lblTimeRemaining;

        #endregion

        #region Constructor

        public PermissionManagementForm()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Starting PermissionManagementForm constructor");
                InitializeComponent();
                System.Diagnostics.Debug.WriteLine("InitializeComponent completed");

                _gridHelper = new PermissionGridHelper();
                System.Diagnostics.Debug.WriteLine("PermissionGridHelper created successfully");

                System.Diagnostics.Debug.WriteLine("PermissionManagementForm constructor completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in PermissionManagementForm constructor: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        #endregion



        #region Form Initialization

        /// <summary>
        /// Handles the form load event - Enhanced with Form Discovery mismatch detection (Task 4)
        /// </summary>
        private async void PermissionManagementForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize progress reporting (Task 4 - Stage 2)
                InitializeProgressReporting();

                // Initialize mismatch detection UI components (Task 4 - Stage 2)
                InitializeMismatchLabels();

                // Initialize the form properly
                InitializeForm();

                // Check for form mismatches (Task 4 - Stage 2: UI updates)
                await CheckForFormMismatchesAsync();

                Debug.WriteLine("PermissionManagementForm loaded successfully with Form Discovery integration");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading Permission Management Form: {ex.Message}");
                SyncLoggingService.LogSyncError("Form load error", ex);
                ShowUserFriendlyError("Failed to initialize form", ex.Message);
            }
        }

        /// <summary>
        /// Initialize form components and setup
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                Debug.WriteLine("Starting InitializeForm");

                // Test database connection first
                Debug.WriteLine("Testing database connection");
                TestDatabaseConnection();
                Debug.WriteLine("Database connection test completed");

                // Set form properties for MDI
                Debug.WriteLine("Setting MDI parent");
                var mdiParent = System.Windows.Forms.Application.OpenForms.OfType<Form>().FirstOrDefault(f => f.IsMdiContainer);
                if (mdiParent != null)
                    this.MdiParent = mdiParent;
                this.WindowState = FormWindowState.Maximized;
                Debug.WriteLine("MDI parent set successfully");

                // Initialize grids
                Debug.WriteLine("Initializing grids");
                InitializeGrids();
                Debug.WriteLine("Grids initialized successfully");

                // Load initial data
                Debug.WriteLine("Loading roles");
                LoadRoles();
                Debug.WriteLine("Roles loaded successfully");

                Debug.WriteLine("Loading users");
                LoadUsers();
                Debug.WriteLine("Users loaded successfully");

                // Setup event handlers
                Debug.WriteLine("Setting up event handlers");
                SetupEventHandlers();
                Debug.WriteLine("Event handlers set up successfully");

                // Add refresh button to form (if it exists)
                AddRefreshButton();

                Debug.WriteLine("PermissionManagementForm initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in InitializeForm: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error initializing Permission Management Form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                // Don't throw - allow form to show even with initialization errors
            }
        }

        /// <summary>
        /// Test database connection and schema
        /// </summary>
        private void TestDatabaseConnection()
        {
            try
            {
                Debug.WriteLine("Testing database connection and schema");

                // Test basic connection
                var connectionManager = ProManage.Modules.Connections.DatabaseConnectionManager.Instance;
                if (!connectionManager.IsConnected)
                {
                    Debug.WriteLine("Database is not connected, attempting to connect");
                    if (!connectionManager.OpenConnection())
                    {
                        Debug.WriteLine($"Database connection failed: {connectionManager.LastError}");
                        // Don't throw exception for database connection issues during form initialization
                        // Just log the error and continue with form initialization
                        return;
                    }
                }
                Debug.WriteLine("Database connection is active");

                // Verify RBAC schema exists
                Debug.WriteLine("Verifying RBAC schema");
                if (!ProManage.Modules.Connections.PermissionDatabaseService.VerifyAndCreateSchema())
                {
                    Debug.WriteLine("Failed to verify or create RBAC database schema");
                    // Don't throw exception for schema issues during form initialization
                    // Just log the error and continue with form initialization
                    return;
                }
                Debug.WriteLine("RBAC schema verified successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Database connection test failed: {ex.Message}");
                // Don't throw exception for database issues during form initialization
                // Just log the error and continue with form initialization
            }
        }

        /// <summary>
        /// Initialize all grids with proper configuration
        /// </summary>
        private void InitializeGrids()
        {
            try
            {
                Debug.WriteLine("Starting InitializeGrids");

                // Check if _gridHelper is null
                if (_gridHelper == null)
                {
                    throw new InvalidOperationException("_gridHelper is null");
                }
                Debug.WriteLine("_gridHelper is not null");

                // Check if grid controls exist
                if (gridControlRolePermissions == null)
                {
                    throw new InvalidOperationException("gridControlRolePermissions is null");
                }
                if (gridViewRolePermissions == null)
                {
                    throw new InvalidOperationException("gridViewRolePermissions is null");
                }
                Debug.WriteLine("Role permission grid controls are not null");

                // Configure role permissions grid
                Debug.WriteLine("Configuring role permissions grid");
                _gridHelper.ConfigureRolePermissionsGrid(gridControlRolePermissions, gridViewRolePermissions);
                gridViewRolePermissions.OptionsBehavior.Editable = false; // Start in read-only mode
                Debug.WriteLine("Role permissions grid configured");

                // Check user permission grid controls
                if (gridControlUserPermissions == null)
                {
                    throw new InvalidOperationException("gridControlUserPermissions is null");
                }
                if (gridViewUserPermissions == null)
                {
                    throw new InvalidOperationException("gridViewUserPermissions is null");
                }
                Debug.WriteLine("User permission grid controls are not null");

                // Configure user permissions grid
                Debug.WriteLine("Configuring user permissions grid");
                _gridHelper.ConfigureUserPermissionsGrid(gridControlUserPermissions, gridViewUserPermissions);
                gridViewUserPermissions.OptionsBehavior.Editable = false; // Start in read-only mode
                Debug.WriteLine("User permissions grid configured");

                // Global permissions are now managed in UserMasterForm
                // Skip global permission checkbox validation since they're moved

                Debug.WriteLine("Grids initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing grids: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// Setup event handlers for form controls
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // Role selection events
                cmbRoles.SelectedIndexChanged += CmbRoles_SelectedIndexChanged;

                // User selection events
                cmbUsers.SelectedIndexChanged += CmbUsers_SelectedIndexChanged;
                // Global permissions user combo removed - functionality moved to UserMasterForm

                // Tab change events
                tabControlMain.SelectedPageChanged += TabControlMain_SelectedPageChanged;

                // Grid events for tracking changes
                gridViewRolePermissions.CellValueChanged += GridViewRolePermissions_CellValueChanged;
                gridViewUserPermissions.CellValueChanged += GridViewUserPermissions_CellValueChanged;

                // Global permission checkboxes removed - now managed in UserMasterForm
                // Event handlers for global permissions removed

                // Button events
                btnSave.Click += BtnSave_Click;
                btnCancel.Click += BtnCancel_Click;
                if (btnRefresh != null)
                    btnRefresh.Click += BtnRefresh_Click;

                btnAddRole.Click += BtnAddRole_Click;

                // Edit/Load button events
                btnRoleEdit.Click += BtnRoleEdit_Click;
                btnRoleLoad.Click += BtnRoleLoad_Click;
                btnDeleteRole.Click += BtnDeleteRole_Click;
                btnUserEdit.Click += BtnUserEdit_Click;
                btnUserLoad.Click += BtnUserLoad_Click;


                Debug.WriteLine("Event handlers setup successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up event handlers: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// Load roles into dropdown
        /// </summary>
        private void LoadRoles()
        {
            try
            {
                Debug.WriteLine("Starting LoadRoles");
                _isLoading = true;

                // Check if cmbRoles exists
                if (cmbRoles == null)
                {
                    throw new InvalidOperationException("cmbRoles is null");
                }
                Debug.WriteLine("cmbRoles is not null");

                Debug.WriteLine("Calling PermissionService.GetAllRoles()");
                var roles = PermissionService.GetAllRoles();

                if (roles == null)
                {
                    Debug.WriteLine("PermissionService.GetAllRoles() returned null - using empty list");
                    roles = new List<ProManage.Modules.Models.PermissionManagementForm.Role>();
                }
                Debug.WriteLine($"PermissionService.GetAllRoles() returned {roles.Count} roles");

                cmbRoles.Properties.Items.Clear();
                cmbRoles.Properties.Items.AddRange(roles.Select(r => $"{r.RoleId}|{r.RoleName}").ToArray());

                if (roles.Any())
                {
                    cmbRoles.SelectedIndex = 0;
                }

                Debug.WriteLine($"Loaded {roles.Count} roles successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LoadRoles: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                // Remove MessageBox to avoid confirmation dialog
                // Don't throw - allow form to continue loading
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// Load users into dropdown
        /// </summary>
        private void LoadUsers()
        {
            try
            {
                Debug.WriteLine("Starting LoadUsers");
                _isLoading = true;

                // Check if user combo boxes exist
                if (cmbUsers == null)
                {
                    throw new InvalidOperationException("cmbUsers is null");
                }
                // cmbUsersGlobal removed - global permissions now managed in UserMasterForm
                // Skip validation for removed global controls
                Debug.WriteLine("User combo boxes are not null");

                Debug.WriteLine("Calling PermissionService.GetAllUsers()");
                var users = PermissionService.GetAllUsers();

                if (users == null)
                {
                    Debug.WriteLine("PermissionService.GetAllUsers() returned null - using empty list");
                    users = new List<ProManage.Modules.Models.PermissionManagementForm.UserInfo>();
                }
                Debug.WriteLine($"PermissionService.GetAllUsers() returned {users.Count} users");

                cmbUsers.Properties.Items.Clear();
                cmbUsers.Properties.Items.AddRange(users.Select(u => $"{u.UserId}|{u.Username} - {u.FullName}").ToArray());

                // Global permissions user combo removed - functionality moved to UserMasterForm
                if (users.Any())
                {
                    cmbUsers.SelectedIndex = 0;
                }

                Debug.WriteLine($"Loaded {users.Count} users successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LoadUsers: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error loading users: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                // Don't throw - allow form to continue loading
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// Load role permissions for selected role
        /// </summary>
        private void LoadRolePermissions()
        {
            if (_currentRoleId <= 0) return;

            try
            {
                _isLoading = true;
                _gridHelper.LoadRolePermissions(gridControlRolePermissions, _currentRoleId);
                Debug.WriteLine($"Loaded permissions for role ID: {_currentRoleId}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading role permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// Load user permissions for selected user
        /// </summary>
        private void LoadUserPermissions()
        {
            if (_currentUserId <= 0) return;

            try
            {
                _isLoading = true;
                _gridHelper.LoadUserPermissions(gridControlUserPermissions, _currentUserId);
                Debug.WriteLine($"Loaded permissions for user ID: {_currentUserId}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading user permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// Refresh all data in the form (roles, users, permissions)
        /// </summary>
        public void RefreshAllData()
        {
            try
            {
                Debug.WriteLine("Refreshing all permission data...");

                // Reload roles and users
                LoadRoles();
                LoadUsers();

                // Reload current permissions if any are selected
                if (_currentRoleId > 0)
                {
                    LoadRolePermissions();
                }

                if (_currentUserId > 0)
                {
                    LoadUserPermissions();
                }

                Debug.WriteLine("Permission data refreshed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing permission data: {ex.Message}");
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// Add refresh button if it doesn't exist
        /// </summary>
        private void AddRefreshButton()
        {
            try
            {
                // btnRefresh should already exist from designer file
                if (btnRefresh != null)
                {
                    Debug.WriteLine("Found existing refresh button from designer");
                    return;
                }

                // Look for existing refresh button in controls
                var refreshButton = this.Controls.Find("btnRefresh", true).FirstOrDefault() as Button;
                if (refreshButton == null)
                {
                    Debug.WriteLine("No refresh button found - this should not happen if designer file is correct");
                }
                else
                {
                    Debug.WriteLine("Found existing refresh button in controls");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking refresh button: {ex.Message}");
            }
        }

        /// <summary>
        /// Load global permissions for selected user - REDIRECTED TO USERMASTERFORM
        /// </summary>
        private void LoadGlobalPermissions()
        {
            // Global permissions are now managed in UserMasterForm
            // Redirect user to UserMasterForm for global permission management
            MessageBox.Show("Global permissions are now managed in the User Management Form.\n\n" +
                           "Please use the User Management Form to modify global permissions for users.",
                           "Global Permissions Moved",
                           MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle role selection change - NO AUTO-LOADING
        /// </summary>
        private void CmbRoles_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                if (cmbRoles.SelectedItem != null)
                {
                    var selectedValue = cmbRoles.SelectedItem.ToString();
                    var parts = selectedValue.Split('|');
                    if (parts.Length >= 2 && int.TryParse(parts[0], out int roleId))
                    {
                        _currentRoleId = roleId;
                        // DO NOT auto-load - user must click Load button
                        ClearRolePermissionsGrid();
                        _hasUnsavedChanges = false;
                        UpdateButtonStates();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error handling role selection: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle user selection change - NO AUTO-LOADING
        /// </summary>
        private void CmbUsers_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                if (cmbUsers.SelectedItem != null)
                {
                    var selectedValue = cmbUsers.SelectedItem.ToString();
                    var parts = selectedValue.Split('|');
                    if (parts.Length >= 2 && int.TryParse(parts[0], out int userId))
                    {
                        _currentUserId = userId;
                        // DO NOT auto-load - user must click Load button
                        ClearUserPermissionsGrid();
                        _hasUnsavedChanges = false;
                        UpdateButtonStates();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error handling user selection: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle global permissions user selection change - REMOVED (functionality moved to UserMasterForm)
        /// </summary>
        private void CmbUsersGlobal_SelectedIndexChanged(object sender, EventArgs e)
        {
            // This method is kept for compatibility but functionality moved to UserMasterForm
            // Global permissions are now managed in UserMasterForm permission tab
        }

        /// <summary>
        /// Handle tab change to synchronize user selection
        /// </summary>
        private void TabControlMain_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            try
            {
                // Synchronize user selection between tabs
                if (e.Page == tabPageUserPermissions && _currentUserId > 0)
                {
                    LoadUserPermissions();
                }
                // Global permissions tab removed - functionality moved to UserMasterForm
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error handling tab change: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle role permissions grid cell value changes
        /// </summary>
        private void GridViewRolePermissions_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (!_isLoading)
            {
                _hasUnsavedChanges = true;
                UpdateButtonStates();
            }
        }

        /// <summary>
        /// Handle user permissions grid cell value changes
        /// </summary>
        private void GridViewUserPermissions_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (!_isLoading)
            {
                _hasUnsavedChanges = true;
                UpdateButtonStates();
                
                // Recalculate effective permissions if user override changed
                if (e.Column.FieldName.StartsWith("User"))
                {
                    RecalculateEffectivePermissions(e.RowHandle);
                }
            }
        }

        /// <summary>
        /// Handle global permission checkbox changes
        /// </summary>
        private void GlobalPermission_CheckedChanged(object sender, EventArgs e)
        {
            if (!_isLoading)
            {
                _hasUnsavedChanges = true;
                UpdateButtonStates();
            }
        }

        #endregion

        #region Button Event Handlers

        /// <summary>
        /// Handle Save button click
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (SaveChanges())
                {
                    _hasUnsavedChanges = false;
                    UpdateButtonStates();
                    MessageBox.Show("Changes saved successfully.", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Reset form after successful save
                    ResetForm();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving changes: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Cancel button click
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                if (_hasUnsavedChanges)
                {
                    var result = MessageBox.Show("You have unsaved changes. Are you sure you want to cancel?",
                        "Confirm Cancel", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.No)
                        return;
                }

                // Reload current data
                if (tabControlMain.SelectedTabPage == tabPageRolePermissions)
                {
                    LoadRolePermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageUserPermissions)
                {
                    LoadUserPermissions();
                }
                // Global permissions tab removed - functionality moved to UserMasterForm

                _hasUnsavedChanges = false;
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error canceling changes: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Refresh button click - Enhanced with Form Discovery sync (Task 4)
        /// </summary>
        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            // Prevent concurrent sync operations (Task 4 - Stage 2)
            lock (_syncLock)
            {
                if (_isSyncInProgress)
                {
                    MessageBox.Show("Sync operation already in progress. Please wait...",
                                  "Sync In Progress", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                _isSyncInProgress = true;
            }

            try
            {
                // Check global sync mutex (app-wide prevention)
                if (!GlobalSyncMutexService.TryAcquireSyncLock(TimeSpan.FromSeconds(30)))
                {
                    MessageBox.Show("Another sync operation is in progress. Please try again later.",
                                  "Sync Locked", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Disable UI during sync (Task 4 - Stage 2)
                SetSyncUIState(true);

                // Initialize cancellation token
                _cancellationTokenSource = new CancellationTokenSource();

                // Execute Form Discovery sync operation (Task 4 - Stage 2)
                await ExecuteFormSyncAsync(_cancellationTokenSource.Token);

                // Traditional refresh operations
                LoadRoles();
                LoadUsers();

                if (tabControlMain.SelectedTabPage == tabPageRolePermissions)
                {
                    LoadRolePermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageUserPermissions)
                {
                    LoadUserPermissions();
                }

                _hasUnsavedChanges = false;
                UpdateButtonStates();

                ShowUserFriendlyMessage("Data refreshed and synchronized successfully.", MessageBoxIcon.Information);
            }
            catch (OperationCanceledException)
            {
                ShowUserFriendlyMessage("Sync operation was cancelled.", MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("Refresh operation failed", ex);
                ShowUserFriendlyError("Refresh operation failed", ex.Message);
            }
            finally
            {
                // Always reset sync state (Task 4 - Stage 2)
                lock (_syncLock)
                {
                    _isSyncInProgress = false;
                }

                // Release global mutex
                GlobalSyncMutexService.ReleaseSyncLock();

                // Re-enable UI
                SetSyncUIState(false);

                // Reset window title
                this.Text = "Permission Management";

                // Dispose cancellation token
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }



        /// <summary>
        /// Handle Add Role button click
        /// </summary>
        private void BtnAddRole_Click(object sender, EventArgs e)
        {
            try
            {
                // Removed debug message box
                Debug.WriteLine("Add Role button clicked");
                
                // Open AddRole form as MDI child
                var addRoleForm = new AddRole();
                Debug.WriteLine($"AddRole form instantiated: {addRoleForm != null}");
                
                if (this.MdiParent == null)
                {
                    Debug.WriteLine("MdiParent is null");
                    MessageBox.Show("MdiParent is null. Cannot set parent for AddRole form.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                
                addRoleForm.MdiParent = this.MdiParent;
                Debug.WriteLine($"MdiParent set: {addRoleForm.MdiParent != null}");
                
                addRoleForm.FormClosed += (s, args) => {
                    // Refresh roles list when AddRole form is closed
                    Debug.WriteLine("AddRole form closed, refreshing roles");
                    LoadRoles();
                };
                
                Debug.WriteLine("About to show AddRole form");
                addRoleForm.Show();
                Debug.WriteLine("AddRole form shown");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in BtnAddRole_Click: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"Error opening Add Role form: {ex.Message}\n\nStack Trace: {ex.StackTrace}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Role Edit button click
        /// </summary>
        private void BtnRoleEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentRoleId <= 0)
                {
                    MessageBox.Show("Please select a role first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Enable grid for editing
                gridViewRolePermissions.OptionsBehavior.Editable = true;
                btnRoleEdit.Enabled = false;

                MessageBox.Show("Role permissions grid is now editable. Make your changes and click Save.", "Edit Mode",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error enabling edit mode: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Role Load button click
        /// </summary>
        private void BtnRoleLoad_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentRoleId <= 0)
                {
                    MessageBox.Show("Please select a role first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                LoadRolePermissions();
                gridViewRolePermissions.OptionsBehavior.Editable = false;
                btnRoleEdit.Enabled = true;
                btnDeleteRole.Enabled = true;
                
                // Removed success message box
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading role permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Delete Role button click
        /// </summary>
        private void BtnDeleteRole_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentRoleId <= 0)
                {
                    MessageBox.Show("Please select a role to delete.", "No Role Selected",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                string roleName = cmbRoles.Text;

                // Check if role is assigned to users
                int userCount = PermissionService.GetRoleUsageCount(_currentRoleId);

                if (userCount < 0)
                {
                    MessageBox.Show("Error checking role usage. Please try again.", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (userCount > 0)
                {
                    MessageBox.Show($"Cannot delete role '{roleName}'. {userCount} user(s) are assigned to this role.\n\n" +
                        "Please reassign users to different roles before deleting.",
                        "Role In Use", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Confirm deletion
                var result = MessageBox.Show($"Are you sure you want to delete role '{roleName}'?\n\n" +
                    "This action will permanently remove the role and all its permissions.",
                    "Confirm Role Deletion", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    if (PermissionService.DeleteRole(_currentRoleId))
                    {
                        MessageBox.Show($"Role '{roleName}' has been successfully deleted.",
                            "Role Deleted", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // Refresh and reset form
                        LoadRoles();
                        ClearRolePermissionsGrid();
                        _currentRoleId = 0;
                        btnRoleEdit.Enabled = false;
                        btnDeleteRole.Enabled = false;
                    }
                    else
                    {
                        MessageBox.Show("Failed to delete role. Please try again.",
                            "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error deleting role: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle User Edit button click
        /// </summary>
        private void BtnUserEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentUserId <= 0)
                {
                    MessageBox.Show("Please select a user first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Enable grid for editing
                gridViewUserPermissions.OptionsBehavior.Editable = true;
                btnUserEdit.Enabled = false;

                MessageBox.Show("User permissions grid is now editable. Make your changes and click Save.", "Edit Mode",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error enabling edit mode: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle User Load button click
        /// </summary>
        private void BtnUserLoad_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentUserId <= 0)
                {
                    MessageBox.Show("Please select a user first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                LoadUserPermissions();
                gridViewUserPermissions.OptionsBehavior.Editable = false;
                btnUserEdit.Enabled = true;

                MessageBox.Show("User permissions loaded successfully.", "Success",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading user permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        #endregion

        #region Helper Methods

        /// <summary>
        /// Update button states based on current form state
        /// </summary>
        private void UpdateButtonStates()
        {
            btnSave.Enabled = _hasUnsavedChanges;
            btnCancel.Enabled = _hasUnsavedChanges;

            // Update Edit button states based on selection
            btnRoleEdit.Enabled = _currentRoleId > 0 && !gridViewRolePermissions.OptionsBehavior.Editable;
            btnUserEdit.Enabled = _currentUserId > 0 && !gridViewUserPermissions.OptionsBehavior.Editable;
        }

        /// <summary>
        /// Recalculate effective permissions for a specific row
        /// </summary>
        private void RecalculateEffectivePermissions(int rowHandle)
        {
            try
            {
                var gridView = gridViewUserPermissions;

                // Get role and user permissions for this row
                var roleRead = (bool)gridView.GetRowCellValue(rowHandle, "RoleRead");
                var roleNew = (bool)gridView.GetRowCellValue(rowHandle, "RoleNew");
                var roleEdit = (bool)gridView.GetRowCellValue(rowHandle, "RoleEdit");
                var roleDelete = (bool)gridView.GetRowCellValue(rowHandle, "RoleDelete");
                var rolePrint = (bool)gridView.GetRowCellValue(rowHandle, "RolePrint");

                var userRead = gridView.GetRowCellValue(rowHandle, "UserRead") as bool?;
                var userNew = gridView.GetRowCellValue(rowHandle, "UserNew") as bool?;
                var userEdit = gridView.GetRowCellValue(rowHandle, "UserEdit") as bool?;
                var userDelete = gridView.GetRowCellValue(rowHandle, "UserDelete") as bool?;
                var userPrint = gridView.GetRowCellValue(rowHandle, "UserPrint") as bool?;

                // Calculate effective permissions (user override takes precedence)
                gridView.SetRowCellValue(rowHandle, "EffectiveRead", userRead ?? roleRead);
                gridView.SetRowCellValue(rowHandle, "EffectiveNew", userNew ?? roleNew);
                gridView.SetRowCellValue(rowHandle, "EffectiveEdit", userEdit ?? roleEdit);
                gridView.SetRowCellValue(rowHandle, "EffectiveDelete", userDelete ?? roleDelete);
                gridView.SetRowCellValue(rowHandle, "EffectivePrint", userPrint ?? rolePrint);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error recalculating effective permissions: {ex.Message}");
            }
        }

        /// <summary>
        /// Save all changes based on current tab
        /// </summary>
        private bool SaveChanges()
        {
            try
            {
                if (tabControlMain.SelectedTabPage == tabPageRolePermissions)
                {
                    return SaveRolePermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageUserPermissions)
                {
                    return SaveUserPermissions();
                }

                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving changes: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Save role permissions from grid
        /// </summary>
        private bool SaveRolePermissions()
        {
            if (_currentRoleId <= 0) return false;

            try
            {
                var permissions = new List<RolePermissionUpdate>();
                var gridView = gridViewRolePermissions;

                for (int i = 0; i < gridView.RowCount; i++)
                {
                    var formName = gridView.GetRowCellValue(i, "FormName")?.ToString();
                    if (string.IsNullOrEmpty(formName)) continue;

                    var permission = new RolePermissionUpdate
                    {
                        RoleId = _currentRoleId,
                        FormName = formName,
                        ReadPermission = (bool)gridView.GetRowCellValue(i, "ReadPermission"),
                        NewPermission = (bool)gridView.GetRowCellValue(i, "NewPermission"),
                        EditPermission = (bool)gridView.GetRowCellValue(i, "EditPermission"),
                        DeletePermission = (bool)gridView.GetRowCellValue(i, "DeletePermission"),
                        PrintPermission = (bool)gridView.GetRowCellValue(i, "PrintPermission")
                    };

                    permissions.Add(permission);
                }

                PermissionService.UpdateRolePermissions(_currentRoleId, permissions);
                PermissionService.ClearCache();

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving role permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Save user permissions from grid
        /// </summary>
        private bool SaveUserPermissions()
        {
            if (_currentUserId <= 0) return false;

            try
            {
                var permissions = new List<UserPermissionUpdate>();
                var gridView = gridViewUserPermissions;

                for (int i = 0; i < gridView.RowCount; i++)
                {
                    var formName = gridView.GetRowCellValue(i, "FormName")?.ToString();
                    if (string.IsNullOrEmpty(formName)) continue;

                    // Get the WritePermission value for both New and Edit permissions
                    var writePermission = gridView.GetRowCellValue(i, "WritePermission") as bool?;

                    var permission = new UserPermissionUpdate
                    {
                        UserId = _currentUserId,
                        FormName = formName,
                        ReadPermission = gridView.GetRowCellValue(i, "ReadPermission") as bool?,
                        NewPermission = writePermission, // WritePermission maps to both New and Edit
                        EditPermission = writePermission,
                        DeletePermission = gridView.GetRowCellValue(i, "DeletePermission") as bool?,
                        PrintPermission = gridView.GetRowCellValue(i, "PrintPermission") as bool?
                    };

                    permissions.Add(permission);
                }

                PermissionService.UpdateUserPermissions(_currentUserId, permissions);
                PermissionService.ClearCache();

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving user permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }





        /// <summary>
        /// Handle form closing to check for unsaved changes
        /// </summary>
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (_hasUnsavedChanges)
            {
                var result = MessageBox.Show("You have unsaved changes. Do you want to save before closing?",
                    "Unsaved Changes", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    if (!SaveChanges())
                    {
                        e.Cancel = true;
                        return;
                    }
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                    return;
                }
            }

            base.OnFormClosing(e);
        }

        /// <summary>
        /// Synchronizes user selection between different parts of the form
        /// </summary>
        /// <param name="userId">User ID to synchronize</param>
        private void SynchronizeUserSelection(int userId)
        {
            // This method synchronizes user selection across different controls
            // Implementation depends on specific UI control names and structure
            // Currently implemented as placeholder to avoid compilation errors
        }

        /// <summary>
        /// Clear role permissions grid
        /// </summary>
        private void ClearRolePermissionsGrid()
        {
            try
            {
                gridControlRolePermissions.DataSource = null;
                gridViewRolePermissions.OptionsBehavior.Editable = false;
                btnRoleEdit.Enabled = false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error clearing role permissions grid: {ex.Message}");
            }
        }

        /// <summary>
        /// Clear user permissions grid
        /// </summary>
        private void ClearUserPermissionsGrid()
        {
            try
            {
                gridControlUserPermissions.DataSource = null;
                gridViewUserPermissions.OptionsBehavior.Editable = false;
                btnUserEdit.Enabled = false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error clearing user permissions grid: {ex.Message}");
            }
        }



        /// <summary>
        /// Reset the entire form to initial state
        /// </summary>
        private void ResetForm()
        {
            try
            {
                _isLoading = true;

                // Reset role tab
                _currentRoleId = 0;
                cmbRoles.SelectedIndex = -1;
                ClearRolePermissionsGrid();

                // Reset user tab
                _currentUserId = 0;
                cmbUsers.SelectedIndex = -1;
                ClearUserPermissionsGrid();

                // Reset state
                _hasUnsavedChanges = false;
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error resetting form: {ex.Message}");
            }
            finally
            {
                _isLoading = false;
            }
        }

        #endregion

        #region Form Discovery Integration (Task 4 Implementation)

        /// <summary>
        /// Initialize progress reporting for sync operations (Task 4 - Stage 2)
        /// </summary>
        private void InitializeProgressReporting()
        {
            _progressReporter = new Progress<SyncProgress>(progress =>
            {
                // Automatically marshaled to UI thread by Progress<T>
                if (InvokeRequired)
                {
                    Invoke(new Action(() => UpdateProgressUI(progress)));
                }
                else
                {
                    UpdateProgressUI(progress);
                }
            });
        }

        /// <summary>
        /// Initialize mismatch detection UI components (Task 4 - Stage 2)
        /// </summary>
        private void InitializeMismatchLabels()
        {
            try
            {
                // Create mismatch status label if it doesn't exist
                if (_lblMismatchStatus == null)
                {
                    _lblMismatchStatus = new Label
                    {
                        Name = "lblMismatchStatus",
                        Text = "Checking for form mismatches...",
                        AutoSize = true,
                        Location = new Point(10, 50),
                        ForeColor = Color.Blue,
                        Visible = false
                    };
                    this.Controls.Add(_lblMismatchStatus);
                }

                // Create mismatch details label if it doesn't exist
                if (_lblMismatchDetails == null)
                {
                    _lblMismatchDetails = new Label
                    {
                        Name = "lblMismatchDetails",
                        Text = "",
                        AutoSize = false,
                        Size = new Size(400, 60),
                        Location = new Point(10, 75),
                        ForeColor = Color.DarkRed,
                        Visible = false
                    };
                    this.Controls.Add(_lblMismatchDetails);
                }

                // Create progress bar if it doesn't exist
                if (_progressBarSync == null)
                {
                    _progressBarSync = new System.Windows.Forms.ProgressBar
                    {
                        Name = "progressBarSync",
                        Size = new Size(300, 20),
                        Location = new Point(10, 140),
                        Visible = false
                    };
                    this.Controls.Add(_progressBarSync);
                }

                // Create operation label if it doesn't exist
                if (_lblCurrentOperation == null)
                {
                    _lblCurrentOperation = new Label
                    {
                        Name = "lblCurrentOperation",
                        Text = "",
                        AutoSize = true,
                        Location = new Point(10, 165),
                        ForeColor = Color.DarkBlue,
                        Visible = false
                    };
                    this.Controls.Add(_lblCurrentOperation);
                }

                // Create progress label if it doesn't exist
                if (_lblProgress == null)
                {
                    _lblProgress = new Label
                    {
                        Name = "lblProgress",
                        Text = "",
                        AutoSize = true,
                        Location = new Point(320, 140),
                        ForeColor = Color.DarkGreen,
                        Visible = false
                    };
                    this.Controls.Add(_lblProgress);
                }

                // Create time remaining label if it doesn't exist
                if (_lblTimeRemaining == null)
                {
                    _lblTimeRemaining = new Label
                    {
                        Name = "lblTimeRemaining",
                        Text = "",
                        AutoSize = true,
                        Location = new Point(320, 165),
                        ForeColor = Color.DarkOrange,
                        Visible = false
                    };
                    this.Controls.Add(_lblTimeRemaining);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing mismatch labels: {ex.Message}");
            }
        }

        /// <summary>
        /// Check for form mismatches asynchronously (Task 4 - Stage 2)
        /// </summary>
        private async Task CheckForFormMismatchesAsync()
        {
            try
            {
                // Check if cache is valid to skip expensive file system scan
                if (FormScanCacheService.ShouldSkipScan())
                {
                    var cache = FormScanCacheService.GetCache();
                    UpdateMismatchLabels(cache?.CachedFormList?.Any() != true);
                    return;
                }

                // Show checking status
                if (_lblMismatchStatus != null)
                {
                    _lblMismatchStatus.Text = "Checking for form mismatches...";
                    _lblMismatchStatus.ForeColor = Color.Blue;
                    _lblMismatchStatus.Visible = true;
                }

                // Perform mismatch detection in background thread
                var mismatchResult = await Task.Run(() => FormDiscoveryService.CompareFormsWithDatabase());

                // Update UI on main thread
                UpdateMismatchLabels(mismatchResult?.HasMismatch ?? false);

                if (mismatchResult?.HasMismatch == true)
                {
                    ShowMismatchDetails(mismatchResult);
                }
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("Mismatch detection error", ex);
                // Don't block form functionality if detection fails
                if (_lblMismatchStatus != null)
                {
                    _lblMismatchStatus.Text = "⚠️ Unable to check for mismatches";
                    _lblMismatchStatus.ForeColor = Color.Orange;
                    _lblMismatchStatus.Visible = true;
                }
            }
        }

        /// <summary>
        /// Update progress UI components (Task 4 - Stage 2)
        /// </summary>
        private void UpdateProgressUI(SyncProgress progress)
        {
            try
            {
                if (_progressBarSync != null)
                    _progressBarSync.Value = Math.Min(progress.PercentComplete, 100);

                if (_lblCurrentOperation != null)
                    _lblCurrentOperation.Text = progress.CurrentOperation;

                if (_lblProgress != null)
                    _lblProgress.Text = $"{progress.CompletedOperations}/{progress.TotalOperations}";

                if (_lblTimeRemaining != null && progress.EstimatedTimeRemaining > TimeSpan.Zero)
                {
                    _lblTimeRemaining.Text = $"Est. {progress.EstimatedTimeRemaining:mm\\:ss} remaining";
                }

                // Update window title with progress for taskbar indication
                if (progress.PercentComplete > 0)
                {
                    this.Text = $"Permission Management - Syncing {progress.PercentComplete}%";
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating progress UI: {ex.Message}");
            }
        }

        /// <summary>
        /// Update mismatch status labels (Task 4 - Stage 2)
        /// </summary>
        private void UpdateMismatchLabels(bool hasMismatch)
        {
            try
            {
                if (_lblMismatchStatus == null) return;

                if (hasMismatch)
                {
                    _lblMismatchStatus.Text = "🔴 Data mismatch – Click Refresh to update.";
                    _lblMismatchStatus.ForeColor = Color.Red;
                    _lblMismatchStatus.Visible = true;
                }
                else
                {
                    _lblMismatchStatus.Text = "✅ Data is up to date.";
                    _lblMismatchStatus.ForeColor = Color.Green;
                    _lblMismatchStatus.Visible = true;

                    // Hide after 3 seconds
                    System.Windows.Forms.Timer hideTimer = new System.Windows.Forms.Timer { Interval = 3000 };
                    hideTimer.Tick += (s, e) =>
                    {
                        if (_lblMismatchStatus != null)
                            _lblMismatchStatus.Visible = false;
                        hideTimer.Stop();
                        hideTimer.Dispose();
                    };
                    hideTimer.Start();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating mismatch labels: {ex.Message}");
            }
        }

        /// <summary>
        /// Show detailed mismatch information (Task 4 - Stage 2)
        /// </summary>
        private void ShowMismatchDetails(FormSyncResult mismatchResult)
        {
            try
            {
                if (_lblMismatchDetails == null || mismatchResult == null) return;

                var details = new StringBuilder();

                if (mismatchResult.MissingForms?.Any() == true)
                {
                    details.AppendLine($"Missing forms ({mismatchResult.MissingForms.Count}):");
                    details.AppendLine(string.Join(", ", mismatchResult.MissingForms.Take(5)));
                    if (mismatchResult.MissingForms.Count > 5)
                        details.AppendLine($"... and {mismatchResult.MissingForms.Count - 5} more");
                }

                if (mismatchResult.ObsoleteForms?.Any() == true)
                {
                    details.AppendLine($"\nObsolete forms ({mismatchResult.ObsoleteForms.Count}):");
                    details.AppendLine(string.Join(", ", mismatchResult.ObsoleteForms.Take(5)));
                    if (mismatchResult.ObsoleteForms.Count > 5)
                        details.AppendLine($"... and {mismatchResult.ObsoleteForms.Count - 5} more");
                }

                _lblMismatchDetails.Text = details.ToString();
                _lblMismatchDetails.Visible = true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error showing mismatch details: {ex.Message}");
            }
        }

        /// <summary>
        /// Set UI state during sync operations (Task 4 - Stage 2)
        /// </summary>
        private void SetSyncUIState(bool syncing)
        {
            try
            {
                // Disable/enable buttons
                if (btnRefresh != null) btnRefresh.Enabled = !syncing;
                if (btnSave != null) btnSave.Enabled = !syncing;

                // Show/hide progress components
                if (_progressBarSync != null) _progressBarSync.Visible = syncing;
                if (_lblCurrentOperation != null) _lblCurrentOperation.Visible = syncing;
                if (_lblProgress != null) _lblProgress.Visible = syncing;
                if (_lblTimeRemaining != null) _lblTimeRemaining.Visible = syncing;

                // Update cursor
                this.Cursor = syncing ? Cursors.WaitCursor : Cursors.Default;

                // Prevent form closing during sync
                this.ControlBox = !syncing;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting sync UI state: {ex.Message}");
            }
        }

        /// <summary>
        /// Show user-friendly error messages (Task 4 - Stage 2)
        /// </summary>
        private void ShowUserFriendlyError(string title, string details)
        {
            var message = $"{title}\n\nDetails: {details}\n\nPlease try again or contact support if the problem persists.";
            MessageBox.Show(message, "Form Discovery Sync Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        /// <summary>
        /// Show user-friendly messages (Task 4 - Stage 2)
        /// </summary>
        private void ShowUserFriendlyMessage(string message, MessageBoxIcon icon)
        {
            MessageBox.Show(message, "Form Discovery Sync", MessageBoxButtons.OK, icon);
        }

        /// <summary>
        /// Report progress for sync operations (Task 4 - Stage 2)
        /// </summary>
        private void ShowProgress(string operation, int completed, int total)
        {
            try
            {
                var progress = new SyncProgress
                {
                    CurrentOperation = operation,
                    CompletedOperations = completed,
                    TotalOperations = total,
                    StartTime = DateTime.Now
                };

                _progressReporter?.Report(progress);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error showing progress: {ex.Message}");
            }
        }

        /// <summary>
        /// Execute Form Discovery sync with progress reporting (Task 4 - Stage 2)
        /// </summary>
        private async Task ExecuteFormSyncAsync(CancellationToken cancellationToken)
        {
            var startTime = DateTime.Now;

            try
            {
                // Show initial progress
                ShowProgress("Initializing sync operation...", 0, 100);

                // Get forms comparison (Stage 1: Background synchronization)
                var comparison = await Task.Run(() => FormDiscoveryService.CompareFormsWithDatabase(), cancellationToken);

                if (comparison == null)
                {
                    ShowUserFriendlyError("Sync failed", "Failed to compare forms with database");
                    return;
                }

                // Check if sync is needed
                if (!comparison.HasMismatch)
                {
                    ShowProgress("No synchronization needed", 100, 100);
                    UpdateMismatchLabels(false);
                    return;
                }

                ShowProgress("Synchronizing forms with database...", 25, 100);

                // Execute batch sync using new SQL procedures (Task 3 integration)
                var syncResult = await PermissionSyncService.ExecuteBatchSyncAsync(
                    comparison.MissingForms,
                    comparison.ObsoleteForms,
                    _progressReporter);

                cancellationToken.ThrowIfCancellationRequested();

                ShowProgress("Updating cache...", 90, 100);

                // Update cache to reflect changes (Stage 2: UI updates)
                if (syncResult.SyncSuccess)
                {
                    var allForms = FormDiscoveryService.GetFormsFromFileSystem();
                    FormScanCacheService.UpdateCache(allForms);
                }

                ShowProgress("Sync completed", 100, 100);

                // Show results to user (Stage 2: UI updates)
                if (syncResult.SyncSuccess)
                {
                    var duration = DateTime.Now - startTime;
                    var message = $"Sync completed successfully!\n\n" +
                                 $"Forms added: {syncResult.MissingForms?.Count ?? 0}\n" +
                                 $"Forms removed: {syncResult.ObsoleteForms?.Count ?? 0}\n" +
                                 $"Users affected: {syncResult.UsersAffected}\n" +
                                 $"Roles affected: {syncResult.RolesAffected}\n" +
                                 $"Duration: {duration:mm\\:ss}";

                    ShowUserFriendlyMessage(message, MessageBoxIcon.Information);
                    UpdateMismatchLabels(false); // No more mismatches
                }
                else
                {
                    var errorMessage = string.Join("\n", syncResult.Errors ?? new List<string>());
                    ShowUserFriendlyError("Sync completed with errors", errorMessage);
                }

                // Refresh grid data to reflect changes (Stage 2: UI updates)
                RefreshPermissionGrids();
            }
            catch (TimeoutException)
            {
                ShowUserFriendlyError("Sync operation timed out",
                    "The sync operation took longer than expected. Please try again or contact support.");
            }
            catch (OperationCanceledException)
            {
                // Re-throw cancellation to be handled by caller
                throw;
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("ExecuteFormSyncAsync", ex);
                ShowUserFriendlyError("Sync operation failed", ex.Message);
            }
        }

        /// <summary>
        /// Refresh permission grids after sync (Task 4 - Stage 2)
        /// </summary>
        private void RefreshPermissionGrids()
        {
            try
            {
                // Reload current permissions if any are selected
                if (_currentRoleId > 0)
                {
                    LoadRolePermissions();
                }

                if (_currentUserId > 0)
                {
                    LoadUserPermissions();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing permission grids: {ex.Message}");
            }
        }

        #endregion


    }
}
